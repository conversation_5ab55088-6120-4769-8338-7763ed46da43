#!/usr/bin/env python3
"""
Test script for the real-time screen streaming functionality.

This script tests the ScreenStreamer and StreamManager classes to ensure
they work correctly before integrating with the MCP server.

Author: inkbytefo
"""

import time
import asyncio
import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from screen_streamer import StreamManager, StreamConfig, get_global_stream_manager
import structlog

# Setup logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

def test_stream_manager():
    """Test basic StreamManager functionality"""
    print("🧪 Testing StreamManager...")
    
    manager = get_global_stream_manager()
    
    # Test stream creation
    print("📝 Creating test stream...")
    stream_id = manager.create_stream(
        fps=2,
        quality=60,
        format="jpeg",
        scale=0.5,
        change_detection=True,
        change_threshold=0.1,
        adaptive_quality=True,
        max_frame_size_kb=300
    )
    
    if not stream_id:
        print("❌ Failed to create stream")
        return False
    
    print(f"✅ Stream created successfully: {stream_id}")
    
    # Wait for a few frames
    print("⏳ Waiting for frames to be captured...")
    time.sleep(5)
    
    # Test getting stream status
    print("📊 Getting stream status...")
    streamer = manager.get_stream(stream_id)
    if streamer:
        status = streamer.get_status()
        print(f"📈 Stream stats:")
        print(f"   - Active: {status['is_active']}")
        print(f"   - Frames captured: {status['stats']['frames_captured']}")
        print(f"   - Current FPS: {status['stats']['current_fps']:.2f}")
        print(f"   - Average frame size: {status['stats']['average_frame_size']:.0f} bytes")
        print(f"   - Buffer size: {status['buffer_info']['current_size']}")
        
        # Test getting current frame
        print("🖼️ Getting current frame...")
        frame = streamer.get_current_frame()
        if frame:
            print(f"✅ Frame retrieved:")
            print(f"   - Frame #{frame.frame_number}")
            print(f"   - Size: {frame.size_bytes} bytes ({frame.size_bytes/1024:.1f} KB)")
            print(f"   - Dimensions: {frame.width}x{frame.height}")
            print(f"   - Format: {frame.format}")
            print(f"   - Data length: {len(frame.data)} chars")
        else:
            print("❌ No frame available")
            return False
    else:
        print("❌ Stream not found")
        return False
    
    # Test stopping stream
    print("🛑 Stopping stream...")
    success = manager.stop_stream(stream_id)
    if success:
        print("✅ Stream stopped successfully")
    else:
        print("❌ Failed to stop stream")
        return False
    
    return True

def test_multiple_streams():
    """Test multiple concurrent streams"""
    print("\n🧪 Testing multiple streams...")
    
    manager = get_global_stream_manager()
    stream_ids = []
    
    # Create multiple streams
    for i in range(3):
        print(f"📝 Creating stream {i+1}...")
        stream_id = manager.create_stream(
            fps=1,
            quality=50,
            format="jpeg",
            scale=0.3,
            change_detection=True
        )
        if stream_id:
            stream_ids.append(stream_id)
            print(f"✅ Stream {i+1} created: {stream_id}")
        else:
            print(f"❌ Failed to create stream {i+1}")
    
    # Wait for frames
    print("⏳ Waiting for frames...")
    time.sleep(3)
    
    # Check all streams status
    all_status = manager.get_all_streams_status()
    print(f"📊 Total streams: {all_status['total_streams']}")
    print(f"📊 Active streams: {all_status['active_streams']}")
    
    # Stop all streams
    print("🛑 Stopping all streams...")
    stopped_count = manager.stop_all_streams()
    print(f"✅ Stopped {stopped_count} streams")
    
    return len(stream_ids) > 0

def test_performance_features():
    """Test performance optimization features"""
    print("\n🧪 Testing performance features...")
    
    manager = get_global_stream_manager()
    
    # Test with change detection enabled
    print("📝 Testing with change detection...")
    stream_id = manager.create_stream(
        fps=5,
        quality=70,
        format="jpeg",
        scale=0.5,
        change_detection=True,
        change_threshold=0.02,  # Very sensitive
        adaptive_quality=True,
        max_frame_size_kb=200
    )
    
    if not stream_id:
        print("❌ Failed to create stream")
        return False
    
    # Wait and check stats
    time.sleep(8)
    
    streamer = manager.get_stream(stream_id)
    if streamer:
        status = streamer.get_status()
        frames_captured = status['stats']['frames_captured']
        frames_skipped = status['stats'].get('frames_skipped', 0)
        
        print(f"📈 Performance stats:")
        print(f"   - Frames captured: {frames_captured}")
        print(f"   - Frames skipped: {frames_skipped}")
        print(f"   - Skip ratio: {frames_skipped/(frames_captured+frames_skipped)*100:.1f}%")
        print(f"   - Average processing time: {status['stats'].get('average_processing_time', 0):.3f}s")
        
        # Stop stream
        manager.stop_stream(stream_id)
        print("✅ Performance test completed")
        return True
    else:
        print("❌ Stream not found")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting ScreenMonitorMCP Streaming Tests")
    print("=" * 50)
    
    try:
        # Test basic functionality
        if not test_stream_manager():
            print("❌ Basic functionality test failed")
            return False
        
        # Test multiple streams
        if not test_multiple_streams():
            print("❌ Multiple streams test failed")
            return False
        
        # Test performance features
        if not test_performance_features():
            print("❌ Performance features test failed")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed successfully!")
        print("✅ Real-time screen streaming is ready for production")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
