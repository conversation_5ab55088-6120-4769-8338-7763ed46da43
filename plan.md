Bu plan, ScreenMonitorMCP projesine gerçek zamanlı ekran görüntüsü akışı özelliği eklemek için gerekli teknik detayları ve uygulama adımlarını içerir. <PERSON><PERSON> ö<PERSON><PERSON>, ekran görüntülerini sürekli olarak yakalayıp base64 formatında kodlayarak MCP istemcilerine (Claude Desktop gibi) sunacaktır.

Teknik Altyapı
1. Ekran Yakalama Sistemi
Ayrı bir thread'de çalışan sürekli bir döngü
Yapılandırılabilir FPS (saniyedeki kare sayısı)
MSS veya PyAutoGUI gibi kütüphaneler kullanarak ekran görüntüsü yakalama
Yakalanan görüntüleri bir dairesel tampon (circular buffer) içinde saklama
2. Görüntü İşleme
Yakalanan görüntüleri JPEG veya PNG formatına dönüştürme
Kalite ve sıkıştırma seviyesi ayarları
Çözünürlük ölçekleme (boyut optimizasyonu için)
Base64 kodlama
3. MCP Araçları
start_screen_stream(): Akışı başlatır ve bir stream_id döndürür
get_stream_frame(stream_id): Belirli bir akışın en son karesini döndürür
get_stream_status(stream_id): Akış durumu ve istatistiklerini döndürür
stop_screen_stream(stream_id): Akışı durdurur
4. Akış Yönetimi
Aktif akışları izleyen global bir yönetici
Birden fazla akış oturumu desteği
Otomatik kaynak temizleme ve zaman aşımı mekanizmaları

Uygulama Bileşenleri
1. ScreenStreamer Sınıfı

class ScreenStreamer:
    def __init__(self, stream_id, fps=5, quality=70, format="jpeg", scale=1.0):
        self.stream_id = stream_id
        self.fps = fps
        self.quality = quality
        self.format = format
        self.scale = scale
        self.is_streaming = False
        self.frame_buffer = deque(maxlen=30)  # Son 30 kareyi sakla
        self.stats = {
            'start_time': None,
            'frames_captured': 0,
            'current_fps': 0,
            'last_frame_time': None
        }
        self.stream_thread = None
        
    def start(self):
        # Akışı başlat
        if self.is_streaming:
            return False
            
        self.is_streaming = True
        self.stats['start_time'] = datetime.now()
        self.stream_thread = threading.Thread(target=self._streaming_loop)
        self.stream_thread.daemon = True
        self.stream_thread.start()
        return True
        
    def stop(self):
        # Akışı durdur
        if not self.is_streaming:
            return False
            
        self.is_streaming = False
        if self.stream_thread:
            self.stream_thread.join(timeout=1.0)
        return True
        
    def get_current_frame(self):
        # En son kareyi döndür
        if not self.frame_buffer:
            return None
        return self.frame_buffer[-1]
        
    def get_status(self):
        # Durum bilgisi döndür
        return {
            'stream_id': self.stream_id,
            'is_active': self.is_streaming,
            'config': {
                'fps': self.fps,
                'quality': self.quality,
                'format': self.format,
                'scale': self.scale
            },
            'stats': self.stats,
            'buffer_size': len(self.frame_buffer)
        }
        
    def _streaming_loop(self):
        # Ana akış döngüsü
        fps_timer = time.time()
        frames_this_second = 0
        
        while self.is_streaming:
            try:
                loop_start = time.time()
                
                # Ekran görüntüsü yakala
                screenshot = capture_screen()
                
                # Görüntüyü işle
                if self.scale != 1.0:
                    screenshot = resize_image(screenshot, self.scale)
                    
                # Görüntüyü kodla
                encoded_image = encode_image(screenshot, self.format, self.quality)
                base64_data = encode_to_base64(encoded_image)
                
                # Kare nesnesini oluştur
                frame = {
                    'timestamp': datetime.now(),
                    'data': base64_data,
                    'format': self.format,
                    'frame_number': self.stats['frames_captured']
                }
                
                # Tampona ekle
                self.frame_buffer.append(frame)
                
                # İstatistikleri güncelle
                self.stats['frames_captured'] += 1
                self.stats['last_frame_time'] = frame['timestamp']
                frames_this_second += 1
                
                # FPS hesapla
                if time.time() - fps_timer >= 1.0:
                    self.stats['current_fps'] = frames_this_second
                    frames_this_second = 0
                    fps_timer = time.time()
                
                # Hedef FPS'i korumak için uyku
                elapsed = time.time() - loop_start
                sleep_time = max(0, 1.0/self.fps - elapsed)
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"Streaming error: {str(e)}")
                time.sleep(0.1)  # Hata durumunda sıkı döngüyü önle


  2. StreamManager Sınıfı

               class StreamManager:
    def __init__(self):
        self.streams = {}  # stream_id -> ScreenStreamer
        self.cleanup_interval = 300  # 5 dakika
        self.last_cleanup = time.time()
        
    def create_stream(self, fps=5, quality=70, format="jpeg", scale=1.0):
        # Yeni akış oluştur
        stream_id = str(uuid.uuid4())
        streamer = ScreenStreamer(stream_id, fps, quality, format, scale)
        self.streams[stream_id] = streamer
        streamer.start()
        return stream_id
        
    def get_stream(self, stream_id):
        # Akış nesnesini döndür
        return self.streams.get(stream_id)
        
    def stop_stream(self, stream_id):
        # Akışı durdur ve temizle
        streamer = self.streams.get(stream_id)
        if not streamer:
            return False
            
        streamer.stop()
        del self.streams[stream_id]
        return True
        
    def cleanup_inactive_streams(self):
        # Belirli bir süre aktif olmayan akışları temizle
        now = time.time()
        if now - self.last_cleanup < self.cleanup_interval:
            return
            
        self.last_cleanup = now
        inactive_streams = []
        
        for stream_id, streamer in self.streams.items():
            if not streamer.is_streaming:
                inactive_streams.append(stream_id)
                
        for stream_id in inactive_streams:
            self.stop_stream(stream_id)

3. Yardımcı Fonksiyonlar

def capture_screen():
    # Ekran görüntüsünü yakala
    with mss.mss() as sct:
        monitor = sct.monitors[0]  # Ana monitör
        sct_img = sct.grab(monitor)
        return np.array(sct_img)
        
def resize_image(image, scale):
    # Görüntüyü yeniden boyutlandır
    height, width = image.shape[:2]
    new_width = int(width * scale)
    new_height = int(height * scale)
    return cv2.resize(image, (new_width, new_height))
    
def encode_image(image, format="jpeg", quality=70):
    # Görüntüyü belirtilen formatta kodla
    if format == "jpeg":
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
        _, buffer = cv2.imencode('.jpg', image, encode_param)
    else:  # png
        _, buffer = cv2.imencode('.png', image)
    return buffer
    
def encode_to_base64(buffer):
    # Buffer'ı base64'e dönüştür
    return base64.b64encode(buffer).decode('utf-8')

4. MCP Araçları
# Global akış yöneticisi
stream_manager = StreamManager()

@mcp.tool()
async def start_screen_stream(
    fps: int = 5,
    quality: int = 70,
    format: Literal["jpeg", "png"] = "jpeg",
    scale: float = 1.0
) -> Dict[str, Any]:
    """
    REVOLUTIONARY FEATURE: Starts real-time base64 encoded screen streaming.
    
    This creates a continuous stream of screen captures that can be accessed
    through the get_stream_frame tool.
    
    Args:
        fps: Frames per second (1-30 recommended)
        quality: Image quality (0-100, JPEG only)
        format: Image format ("jpeg" or "png")
        scale: Scale factor for resolution (0.5 = half size)
        
    Returns:
        Stream information including stream_id
    """
    stream_id = stream_manager.create_stream(fps, quality, format, scale)
    
    streamer = stream_manager.get_stream(stream_id)
    if not streamer:
        return {
            "status": "error",
            "message": "Failed to create stream"
        }
    
    return {
        "status": "streaming_started",
        "stream_id": stream_id,
        "config": {
            "fps": fps,
            "quality": quality,
            "format": format,
            "scale": scale
        }
    }

@mcp.tool()
async def get_stream_frame(
    stream_id: str
) -> Dict[str, Any]:
    """
    Gets the most recent frame from a screen stream.
    
    Args:
        stream_id: The ID of the stream to get a frame from
        
    Returns:
        The latest frame as base64 encoded string with metadata
    """
    streamer = stream_manager.get_stream(stream_id)
    if not streamer:
        return {
            "status": "error",
            "message": "Stream not found"
        }
    
    frame = streamer.get_current_frame()
    if not frame:
        return {
            "status": "no_frame",
            "message": "No frames captured yet"
        }
    
    return {
        "status": "success",
        "frame": {
            "timestamp": frame['timestamp'].isoformat(),
            "frame_number": frame['frame_number'],
            "format": frame['format'],
            "data": frame['data']
        }
    }

@mcp.tool()
async def get_stream_status(
    stream_id: str
) -> Dict[str, Any]:
    """
    Gets the current status of a screen stream.
    
    Args:
        stream_id: The ID of the stream to check
        
    Returns:
        Detailed streaming statistics and status
    """
    streamer = stream_manager.get_stream(stream_id)
    if not streamer:
        return {
            "status": "error",
            "message": "Stream not found"
        }
    
    return {
        "status": "success",
        "stream_info": streamer.get_status()
    }

@mcp.tool()
async def stop_screen_stream(
    stream_id: str
) -> Dict[str, Any]:
    """
    Stops a screen stream.
    
    Args:
        stream_id: The ID of the stream to stop
        
    Returns:
        Status of the operation
    """
    success = stream_manager.stop_stream(stream_id)
    
    if success:
        return {
            "status": "success",
            "message": "Stream stopped successfully"
        }
    else:
        return {
            "status": "error",
            "message": "Failed to stop stream or stream not found"
        }
    
Claude Desktop Kullanım Senaryosu
Claude Desktop, MCP protokolünü kullanarak bu araçları çağırabilir ancak WebSocket gibi sürekli bağlantıları desteklemez. Bu nedenle, polling (düzenli sorgulama) yaklaşımı kullanılmalıdır:

Kullanıcı "Ekranımı izlemeye başla" dediğinde:
Claude start_screen_stream() aracını çağırır
Dönen stream_id değerini saklar
Claude düzenli aralıklarla (örn. her 200-500ms):
get_stream_frame(stream_id) ile en son kareyi alır
Base64 verisini görüntüye dönüştürür ve analiz eder
Gerekirse get_stream_status(stream_id) ile durum bilgisi alır
Kullanıcı "İzlemeyi durdur" dediğinde:
Claude stop_screen_stream(stream_id) aracını çağırır
Performans Optimizasyonları
Çözünürlük Ölçekleme: Tam çözünürlük yerine ölçeklendirilmiş görüntüler kullanarak veri boyutunu azaltın (scale parametresi)
JPEG Sıkıştırma: PNG yerine JPEG kullanarak veri boyutunu azaltın ve kalite parametresiyle dengeyi ayarlayın
FPS Optimizasyonu: Düşük FPS değerleri kullanarak (3-5 FPS) sistem yükünü ve veri hacmini azaltın
Değişiklik Algılama: Sadece ekranda önemli değişiklikler olduğunda yeni kare göndererek gereksiz veri aktarımını önleyin
Tampon Yönetimi: Sınırlı sayıda kare saklayarak bellek kullanımını optimize edin
Otomatik Temizleme: Kullanılmayan akışları otomatik olarak temizleyerek kaynak sızıntılarını önleyin
Uygulama Adımları
ScreenStreamer ve StreamManager sınıflarını oluşturun
Ekran yakalama ve görüntü işleme fonksiyonlarını yazın
MCP araçlarını tanımlayın ve global akış yöneticisine bağlayın
Hata işleme ve loglama mekanizmalarını ekleyin
Performans optimizasyonlarını uygulayın
Claude Desktop ile entegrasyonu test edin
Teknik Gereksinimler
Kütüphaneler:
mss: Hızlı ekran yakalama
opencv-python: Görüntü işleme
numpy: Dizi işlemleri
base64: Base64 kodlama/çözme
Sistem Gereksinimleri:
Ekran yakalama izinleri
Yeterli CPU ve bellek kaynakları
Güvenlik Hususları:
Ekran içeriği hassas bilgiler içerebilir, bu nedenle veri güvenliği önemlidir
Akış verilerinin şifrelenmesi veya güvenli kanallar üzerinden iletilmesi düşünülebilir
Bu plan, ScreenMonitorMCP projesine gerçek zamanlı ekran akışı özelliği eklemek için kapsamlı bir teknik çerçeve sunmaktadır. MCP protokolü üzerinden polling yaklaşımı kullanarak, Claude Desktop gibi istemcilerin bu özelliği kullanmasını sağlayabilirsiniz.